import React, { useState } from 'react';
import api from '../../../utils/api';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import GenericDataTable from 'components/tables/GenericDataTable';
import FormInput from '../components/inputform/FormInput';

import { FormMonthPicker } from '../../../components/forms';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

// 定义主题和样式
const styles = {
  searchResults: {
    marginTop: 1.5,
    marginBottom: 1.5,
    padding: 1.5,
    borderRadius: 1,
    boxShadow: '0 2px 10px 0 rgba(0,0,0,0.08)',
    backgroundColor: '#f9f9f9',
  },
  resultsTitle: {
    fontSize: '15px',
    fontWeight: 500,
    color: '#3f51b5',
    marginBottom: 0.8,
  },
  noResults: {
    textAlign: 'center',
    padding: 1.5,
    color: '#757575',
    fontSize: '13px',
  },
  root: {
    '& .MuiTextField-root': {
      marginBottom: 1.5,
    },
  },
  paper: {
    padding: 2,
    margin: '10px 0',
    borderRadius: 1,
    boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '18px',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 1.5,
    textAlign: 'center',
  },
  section: {
    marginTop: 1.5,
  },
  sectionTitle: {
    fontSize: '15px',
    fontWeight: 500,
    color: '#283593',
    marginBottom: 1,
  },
  submitButton: {
    marginTop: 1.5,
    padding: '8px 0',
    backgroundColor: '#1976d2',
    color: '#ffffff',
    fontWeight: 500,
    '&:hover': {
      backgroundColor: '#1565c0',
    },
    fontSize: '13px',
    height: '36px',
    boxShadow: 'none',
    borderRadius: '4px',
    textTransform: 'none',
  },
  resetButton: {
    marginTop: 1.5,
    padding: '8px 0',
    backgroundColor: '#f5f5f5',
    color: '#333',
    '&:hover': {
      backgroundColor: '#e0e0e0',
    },
    fontSize: '13px',
    height: '36px',
    boxShadow: 'none',
    borderRadius: '4px',
    textTransform: 'none',
  },
  conversionDirectionBox: {
    padding: 1.5,
    borderRadius: 1,
    border: '1px solid #e0e0e0',
    backgroundColor: '#fafafa',
  },
  radioGroup: {
    '& .MuiFormControlLabel-root': {
      marginBottom: 0.5,
    },
    '& .MuiRadio-root': {
      padding: '4px 8px',
    },
  },
};

const LitigationConversion = () => {
  // 获取当前日期
  const currentDate = new Date();

  // 表单状态
  const [formData, setFormData] = useState({
    creditor: '',
    debtor: '',
    conversionDirection: 'litigation_to_non_litigation', // 默认：诉讼转非诉讼
    conversionYear: currentDate.getFullYear(),
    conversionMonth: currentDate.getMonth() + 1,
    remark: '',
    // 非诉讼转诉讼特有字段
    litigationCase: '',
    litigationOccurredPrincipal: '',
    litigationInterestFee: '',
    litigationFee: '',
    intermediaryFee: '',
  });

  const [errors, setErrors] = useState({});
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 年月选择器选项（支持2020-2030年的数据）
  const yearMonthOptions = [];
  for (let y = 2020; y <= 2030; y++) {
    for (let m = 1; m <= 12; m++) {
      const mStr = m.toString().padStart(2, '0');
      yearMonthOptions.push({
        value: `${y}-${mStr}`,
        label: `${y}年${m}月`,
      });
    }
  }

  // 自动搜索功能
  const autoSearch = async (creditor, debtor) => {
    if (!creditor && !debtor) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      const queryParams = new URLSearchParams();
      if (creditor) {
        queryParams.append('creditor', creditor);
      }
      if (debtor) {
        queryParams.append('debtor', debtor);
      }

      const response = await api.get(`/debts/conversion/search?${queryParams.toString()}`);
      if (response.status === 200) {
        setSearchResults(response.data || []);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理转换方向变化
  const handleConversionDirectionChange = event => {
    const newDirection = event.target.value;
    setFormData({
      ...formData,
      conversionDirection: newDirection,
      // 清空诉讼特有字段
      litigationCase: '',
      litigationOccurredPrincipal: '',
      litigationInterestFee: '',
      litigationFee: '',
      intermediaryFee: '',
    });
    setSelectedRecord(null);
  };

  // 处理记录选择
  const handleRecordSelect = record => {
    setSelectedRecord(record);
    // 根据转换方向过滤可选记录
    const isValidSelection =
      (formData.conversionDirection === 'litigation_to_non_litigation' &&
        record.currentStatus === '诉讼') ||
      (formData.conversionDirection === 'non_litigation_to_litigation' &&
        record.currentStatus === '非诉讼');

    if (!isValidSelection) {
      alert(`当前选择的转换方向不匹配该记录的状态。记录状态：${record.currentStatus}`);
      setSelectedRecord(null);
    }
  };

  // 表单验证
  const validate = () => {
    const newErrors = {};

    if (!formData.creditor) {
      newErrors.creditor = '请输入债权人';
    }
    if (!formData.debtor) {
      newErrors.debtor = '请输入债务人';
    }
    if (!selectedRecord) {
      newErrors.selectedRecord = '请选择要转换的债权记录';
    }

    // 非诉讼转诉讼时的特殊验证
    if (formData.conversionDirection === 'non_litigation_to_litigation') {
      if (!formData.litigationCase) {
        newErrors.litigationCase = '非诉讼转诉讼时诉讼案件名称为必填项';
      }
    }

    return newErrors;
  };

  // 提交处理
  const handleSubmit = async e => {
    e.preventDefault();

    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    if (!selectedRecord) {
      alert('请先选择要转换的债权记录');
      return;
    }

    // 确认转换操作
    const conversionText =
      formData.conversionDirection === 'litigation_to_non_litigation'
        ? '诉讼转非诉讼'
        : '非诉讼转诉讼';
    const targetStatus =
      formData.conversionDirection === 'litigation_to_non_litigation' ? '非诉讼' : '诉讼';

    const confirmMessage = `确认要执行${conversionText}吗？
    
债权人：${selectedRecord.creditor}
债务人：${selectedRecord.debtor}
当前状态：${selectedRecord.currentStatus}
转换后状态：${targetStatus}
转换年月：${formData.conversionYear}年${formData.conversionMonth}月

该操作不可撤销，请确认！`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 准备请求数据
      const requestData = {
        creditor: selectedRecord.creditor,
        debtor: selectedRecord.debtor,
        period: selectedRecord.period,
        year: selectedRecord.year,
        month: selectedRecord.month,
        conversionYear: formData.conversionYear,
        conversionMonth: formData.conversionMonth,
        remark: formData.remark,
      };

      // 如果是非诉讼转诉讼，添加诉讼特有字段
      if (formData.conversionDirection === 'non_litigation_to_litigation') {
        requestData.litigationCase = formData.litigationCase;
        requestData.litigationOccurredPrincipal =
          parseFloat(formData.litigationOccurredPrincipal) || 0;
        requestData.litigationInterestFee = parseFloat(formData.litigationInterestFee) || 0;
        requestData.litigationFee = parseFloat(formData.litigationFee) || 0;
        requestData.intermediaryFee = parseFloat(formData.intermediaryFee) || 0;
      }

      // 调用对应的API端点
      const endpoint =
        formData.conversionDirection === 'litigation_to_non_litigation'
          ? '/debts/conversion/litigation-to-non-litigation'
          : '/debts/conversion/non-litigation-to-litigation';

      const response = await api.post(endpoint, requestData);

      if (response.status === 200 && response.data.success) {
        alert(`${conversionText}执行成功！\n\n${response.data.message}`);

        // 重置表单和状态
        resetForm();

        // 刷新搜索结果
        if (formData.creditor && formData.debtor) {
          setTimeout(() => {
            autoSearch(formData.creditor, formData.debtor);
          }, 1000);
        }
      } else {
        alert(`${conversionText}失败：${response.data?.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('转换操作失败:', error);

      let errorMessage = `${conversionText}失败`;
      if (error.response?.data?.message) {
        errorMessage += `：${error.response.data.message}`;
      } else if (error.message) {
        errorMessage += `：${error.message}`;
      }

      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      creditor: '',
      debtor: '',
      conversionDirection: 'litigation_to_non_litigation',
      conversionYear: currentDate.getFullYear(),
      conversionMonth: currentDate.getMonth() + 1,
      remark: '',
      litigationCase: '',
      litigationOccurredPrincipal: '',
      litigationInterestFee: '',
      litigationFee: '',
      intermediaryFee: '',
    });
    setErrors({});
    setSearchResults([]);
    setHasSearched(false);
    setSelectedRecord(null);
  };

  // 格式化货币显示
  const formatCurrency = amount => {
    if (amount === null || amount === undefined) {
      return '-';
    }
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // 处理转换年月变化
  const handleConversionYearMonthChange = e => {
    const [year, month] = e.target.value.split('-');
    setFormData({
      ...formData,
      conversionYear: parseInt(year),
      conversionMonth: parseInt(month),
    });
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h5" sx={styles.title}>
              诉讼和非诉讼互转
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                {/* 基本信息部分 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    基本信息
                  </Typography>
                  <Grid container spacing={1.5}>
                    <Grid item xs={12} md={6}>
                      <FormInput
                        label="债权人"
                        value={formData.creditor}
                        onChange={e => {
                          const newValue = e.target.value;
                          setFormData({ ...formData, creditor: newValue });
                          if (newValue && formData.debtor) {
                            autoSearch(newValue, formData.debtor);
                          }
                        }}
                        required={true}
                        error={errors.creditor}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormInput
                        label="债务人"
                        value={formData.debtor}
                        onChange={e => {
                          const newValue = e.target.value;
                          setFormData({ ...formData, debtor: newValue });
                          if (formData.creditor && newValue) {
                            autoSearch(formData.creditor, newValue);
                          }
                        }}
                        required={true}
                        error={errors.debtor}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* 搜索结果显示区域 */}
                {hasSearched && (
                  <Grid item xs={12}>
                    <Box sx={styles.searchResults}>
                      <Typography variant="subtitle1" sx={styles.resultsTitle}>
                        可转换的债权记录 ({searchResults.length} 条记录)
                      </Typography>
                      {isSearching ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : searchResults.length > 0 ? (
                        <GenericDataTable
                          columns={[
                            { field: 'creditor', headerName: '债权人', width: '12%' },
                            { field: 'debtor', headerName: '债务人', width: '12%' },
                            { field: 'period', headerName: '期间', width: '10%' },
                            { field: 'year', headerName: '年份', width: '8%' },
                            { field: 'month', headerName: '月份', width: '8%' },
                            { field: 'currentStatus', headerName: '当前状态', width: '8%' },
                            { field: 'managementCompany', headerName: '管理公司', width: '10%' },
                            {
                              field: 'debtBalance',
                              headerName: '债权余额',
                              width: '10%',
                              type: 'number',
                            },
                          ]}
                          data={searchResults.map((item, idx) => ({
                            id: idx,
                            ...item,
                            selected: selectedRecord && selectedRecord.id === idx,
                          }))}
                          rowHeight={40}
                          compact={true}
                          formatCurrency={formatCurrency}
                          renderActions={row => (
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%',
                              }}
                              onClick={() => handleRecordSelect({ ...row, id: row.id })}
                            >
                              <Box
                                sx={{
                                  width: '18px',
                                  height: '18px',
                                  border: '1px solid #aaa',
                                  borderRadius: '2px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  bgcolor: row.selected ? '#1976d2' : 'transparent',
                                  color: 'white',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: row.selected ? '#1565c0' : '#f5f5f5',
                                  },
                                }}
                              >
                                {row.selected && '✓'}
                              </Box>
                            </Box>
                          )}
                          actionColumnWidth="12%"
                          actionColumnTitle="选择"
                        />
                      ) : (
                        <Typography sx={styles.noResults}>
                          未找到匹配的债权记录，请检查债权人和债务人信息是否正确
                        </Typography>
                      )}
                      {errors.selectedRecord && (
                        <Typography color="error" sx={{ mt: 1, fontSize: '12px' }}>
                          {errors.selectedRecord}
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                )}

                {/* 转换方向选择 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    转换方向
                  </Typography>
                  <Box sx={styles.conversionDirectionBox}>
                    <FormControl component="fieldset">
                      <RadioGroup
                        value={formData.conversionDirection}
                        onChange={handleConversionDirectionChange}
                        sx={styles.radioGroup}
                      >
                        <FormControlLabel
                          value="litigation_to_non_litigation"
                          control={<Radio />}
                          label="诉讼转非诉讼"
                        />
                        <FormControlLabel
                          value="non_litigation_to_litigation"
                          control={<Radio />}
                          label="非诉讼转诉讼"
                        />
                      </RadioGroup>
                    </FormControl>
                  </Box>
                </Grid>

                {/* 转换信息 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    转换信息
                  </Typography>
                  <Grid container spacing={1.5}>
                    <Grid item xs={12} md={4}>
                      <FormMonthPicker
                        label="转换年月"
                        value={`${formData.conversionYear}-${formData.conversionMonth
                          .toString()
                          .padStart(2, '0')}`}
                        onChange={handleConversionYearMonthChange}
                        required={true}
                        name="conversionYearMonth"
                      />
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <FormInput
                        label="备注说明"
                        value={formData.remark}
                        onChange={e => setFormData({ ...formData, remark: e.target.value })}
                        multiline
                        rows={2}
                      />
                    </Grid>
                  </Grid>
                </Grid>

                {/* 非诉讼转诉讼特有字段 */}
                {formData.conversionDirection === 'non_litigation_to_litigation' && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={styles.sectionTitle}>
                      诉讼信息
                    </Typography>
                    <Grid container spacing={1.5}>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="诉讼案件名称"
                          value={formData.litigationCase}
                          onChange={e =>
                            setFormData({ ...formData, litigationCase: e.target.value })
                          }
                          required={true}
                          error={errors.litigationCase}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="诉讼主张本金"
                          value={formData.litigationOccurredPrincipal}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationOccurredPrincipal: e.target.value,
                            })
                          }
                          type="number"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="诉讼主张利息及罚金"
                          value={formData.litigationInterestFee}
                          onChange={e =>
                            setFormData({ ...formData, litigationInterestFee: e.target.value })
                          }
                          type="number"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="诉讼费"
                          value={formData.litigationFee}
                          onChange={e =>
                            setFormData({ ...formData, litigationFee: e.target.value })
                          }
                          type="number"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="中介费"
                          value={formData.intermediaryFee}
                          onChange={e =>
                            setFormData({ ...formData, intermediaryFee: e.target.value })
                          }
                          type="number"
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                )}

                {/* 按钮部分 */}
                <Grid item xs={12}>
                  <Box sx={{ mt: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Button
                          variant="contained"
                          fullWidth
                          type="submit"
                          sx={styles.submitButton}
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <CircularProgress size={16} sx={{ mr: 1 }} />
                              处理中...
                            </>
                          ) : formData.conversionDirection === 'litigation_to_non_litigation' ? (
                            '转为非诉讼'
                          ) : (
                            '转为诉讼'
                          )}
                        </Button>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={resetForm}
                          sx={styles.resetButton}
                          disabled={isSubmitting}
                        >
                          重置
                        </Button>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default LitigationConversion;
