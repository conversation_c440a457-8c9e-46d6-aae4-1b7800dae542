package com.laoshu198838.controller.debt;

import com.laoshu198838.dto.debt.DebtConversionRequestDTO;
import com.laoshu198838.dto.debt.DebtConversionResponseDTO;
import com.laoshu198838.dto.debt.DebtSearchResultDTO;
import com.laoshu198838.service.DebtConversionService;

import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 债权转换控制器
 * 提供诉讼与非诉讼债权相互转换的API接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/debts/conversion")
@CrossOrigin(origins = {"http://localhost:3000", "http://**********:3000"})
public class DebtConversionController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionController.class);
    
    private final DebtConversionService debtConversionService;
    
    public DebtConversionController(DebtConversionService debtConversionService) {
        this.debtConversionService = debtConversionService;
    }
    
    /**
     * 搜索可转换的债权记录
     * 
     * @param creditor 债权人（可选）
     * @param debtor 债务人（可选）
     * @return 匹配的债权记录列表
     */
    @GetMapping("/search")
    public ResponseEntity<List<DebtSearchResultDTO>> searchConvertibleDebts(
            @RequestParam(required = false) String creditor,
            @RequestParam(required = false) String debtor) {
        
        logger.info("======== 债权转换搜索请求 ========");
        logger.info("债权人: {}", creditor);
        logger.info("债务人: {}", debtor);
        logger.info("================================");
        
        try {
            List<DebtSearchResultDTO> results = debtConversionService.searchConvertibleDebts(creditor, debtor);
            
            logger.info("搜索完成，返回 {} 条记录", results.size());
            
            return ResponseEntity.ok(results);
            
        } catch (Exception e) {
            logger.error("搜索可转换债权记录时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @PostMapping("/litigation-to-non-litigation")
    public ResponseEntity<DebtConversionResponseDTO> convertLitigationToNonLitigation(
            @Valid @RequestBody DebtConversionRequestDTO request) {
        
        logger.info("======== 诉讼转非诉讼请求 ========");
        logger.info("债权人: {}", request.getCreditor());
        logger.info("债务人: {}", request.getDebtor());
        logger.info("期间: {}", request.getPeriod());
        logger.info("转换年月: {}-{}", request.getConversionYear(), request.getConversionMonth());
        logger.info("备注: {}", request.getRemark());
        logger.info("===============================");
        
        try {
            DebtConversionResponseDTO response = debtConversionService.convertLitigationToNonLitigation(request);
            
            if (response.isSuccess()) {
                logger.info("诉讼转非诉讼执行成功: {}", response.getMessage());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("诉讼转非诉讼执行失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("诉讼转非诉讼处理过程中发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "诉讼转非诉讼失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 智能债权转换
     * 根据选中债权的当前状态自动调整转换方向
     * 如果选中的是非诉讼债权，则执行非诉讼转诉讼；反之则执行诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @PostMapping("/smart-convert")
    public ResponseEntity<DebtConversionResponseDTO> smartConvert(
            @Valid @RequestBody DebtConversionRequestDTO request) {
        
        logger.info("======== 智能债权转换请求 ========");
        logger.info("债权人: {}", request.getCreditor());
        logger.info("债务人: {}", request.getDebtor());
        logger.info("期间: {}", request.getPeriod());
        logger.info("原始年月: {}-{}", request.getYear(), request.getMonth());
        logger.info("转换年月: {}-{}", request.getConversionYear(), request.getConversionMonth());
        logger.info("备注: {}", request.getRemark());
        logger.info("==============================");
        
        try {
            // 1. 自动检测债权当前状态
            String currentStatus = detectCurrentDebtStatus(request);
            logger.info("检测到债权当前状态: {}", currentStatus);
            
            // 2. 根据当前状态选择转换方向
            DebtConversionResponseDTO response;
            if ("非诉讼".equals(currentStatus)) {
                logger.info("执行：非诉讼 → 诉讼");
                response = debtConversionService.convertNonLitigationToLitigation(request);
                // 添加状态转换信息
                if (response.isSuccess()) {
                    response.setFromStatus("非诉讼");
                    response.setToStatus("诉讼");
                    response.setMessage("债权已成功从非诉讼转换为诉讼状态");
                }
            } else if ("诉讼".equals(currentStatus)) {
                logger.info("执行：诉讼 → 非诉讼");
                response = debtConversionService.convertLitigationToNonLitigation(request);
                // 添加状态转换信息
                if (response.isSuccess()) {
                    response.setFromStatus("诉讼");
                    response.setToStatus("非诉讼");
                    response.setMessage("债权已成功从诉讼转换为非诉讼状态");
                }
            } else {
                String errorMsg = "无法确定债权当前状态，无法执行智能转换。可能原因：1) 债权记录不存在；2) 债权状态异常";
                logger.error(errorMsg + "，检测到的状态: {}", currentStatus);
                DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                    errorMsg, "UNKNOWN_STATUS"
                );
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            if (response.isSuccess()) {
                logger.info("智能债权转换执行成功: {}", response.getMessage());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("智能债权转换执行失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("智能债权转换处理过程中发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "智能债权转换失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 检测债权当前状态
     * 通过查询诉讼表和非诉讼表来确定债权的当前状态
     * 
     * @param request 转换请求数据
     * @return 当前状态（"诉讼"或"非诉讼"）
     */
    private String detectCurrentDebtStatus(DebtConversionRequestDTO request) {
        try {
            // 构建搜索条件，查找该债权的当前状态
            List<DebtSearchResultDTO> searchResults = debtConversionService.searchConvertibleDebts(
                request.getCreditor(), request.getDebtor()
            );
            
            // 在搜索结果中查找匹配的债权记录
            for (DebtSearchResultDTO result : searchResults) {
                if (result.getCreditor().equals(request.getCreditor()) &&
                    result.getDebtor().equals(request.getDebtor()) &&
                    result.getPeriod().equals(request.getPeriod()) &&
                    result.getYear().equals(request.getYear()) &&
                    result.getMonth().equals(request.getMonth())) {
                    
                    logger.info("找到匹配的债权记录，当前状态: {}, 是否涉诉: {}, 来源表: {}", 
                        result.getCurrentStatus(), result.getIsLitigation(), result.getSourceTable());
                    
                    // 返回当前状态
                    return result.getCurrentStatus();
                }
            }
            
            logger.warn("未找到匹配的债权记录，无法确定当前状态");
            return "UNKNOWN";
            
        } catch (Exception e) {
            logger.error("检测债权当前状态时发生错误", e);
            return "ERROR";
        }
    }
    
    /**
     * 检测债权转换方向
     * 用于前端预先检测债权状态并显示转换方向
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @return 债权状态检测结果
     */
    @GetMapping("/detect-conversion-direction")
    public ResponseEntity<DebtConversionResponseDTO> detectConversionDirection(
            @RequestParam String creditor,
            @RequestParam String debtor,
            @RequestParam String period,
            @RequestParam Integer year,
            @RequestParam Integer month) {
        
        logger.info("======== 检测债权转换方向 ========");
        logger.info("债权人: {}, 债务人: {}, 期间: {}, 年月: {}-{}", creditor, debtor, period, year, month);
        
        try {
            // 构建临时请求对象用于检测
            DebtConversionRequestDTO tempRequest = DebtConversionRequestDTO.builder()
                .creditor(creditor)
                .debtor(debtor)
                .period(period)
                .year(year)
                .month(month)
                .build();
            
            // 检测当前状态
            String currentStatus = detectCurrentDebtStatus(tempRequest);
            logger.info("检测结果 - 当前状态: {}", currentStatus);
            
            // 构建响应
            DebtConversionResponseDTO response;
            if ("非诉讼".equals(currentStatus)) {
                response = DebtConversionResponseDTO.builder()
                    .success(true)
                    .message("检测到非诉讼债权，可转换为诉讼债权")
                    .conversionType("non_litigation_to_litigation")
                    .fromStatus("非诉讼")
                    .toStatus("诉讼")
                    .details("该债权当前为非诉讼状态，可以转换为诉讼状态")
                    .timestamp(java.time.LocalDateTime.now().toString())
                    .build();
            } else if ("诉讼".equals(currentStatus)) {
                response = DebtConversionResponseDTO.builder()
                    .success(true)
                    .message("检测到诉讼债权，可转换为非诉讼债权")
                    .conversionType("litigation_to_non_litigation")
                    .fromStatus("诉讼")
                    .toStatus("非诉讼")
                    .details("该债权当前为诉讼状态，可以转换为非诉讼状态")
                    .timestamp(java.time.LocalDateTime.now().toString())
                    .build();
            } else {
                response = DebtConversionResponseDTO.builder()
                    .success(false)
                    .message("无法检测到有效的债权状态")
                    .details("未找到对应的债权记录或债权状态异常，无法确定转换方向")
                    .timestamp(java.time.LocalDateTime.now().toString())
                    .build();
            }
            
            logger.info("转换方向检测完成: {}", response.getMessage());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("检测债权转换方向时发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "检测转换方向失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 非诉讼转诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @PostMapping("/non-litigation-to-litigation")
    public ResponseEntity<DebtConversionResponseDTO> convertNonLitigationToLitigation(
            @Valid @RequestBody DebtConversionRequestDTO request) {
        
        logger.info("======== 非诉讼转诉讼请求 ========");
        logger.info("债权人: {}", request.getCreditor());
        logger.info("债务人: {}", request.getDebtor());
        logger.info("期间: {}", request.getPeriod());
        logger.info("转换年月: {}-{}", request.getConversionYear(), request.getConversionMonth());
        logger.info("诉讼案件: {}", request.getLitigationCase());
        logger.info("诉讼主张本金: {}", request.getLitigationOccurredPrincipal());
        logger.info("诉讼主张利息及罚金: {}", request.getLitigationInterestFee());
        logger.info("诉讼费: {}", request.getLitigationFee());
        logger.info("中介费: {}", request.getIntermediaryFee());
        logger.info("备注: {}", request.getRemark());
        logger.info("===============================");
        
        try {
            // 验证非诉讼转诉讼的必填字段
            if (request.getLitigationCase() == null || request.getLitigationCase().trim().isEmpty()) {
                DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                    "非诉讼转诉讼时诉讼案件名称为必填项",
                    "ValidationError"
                );
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            DebtConversionResponseDTO response = debtConversionService.convertNonLitigationToLitigation(request);
            
            if (response.isSuccess()) {
                logger.info("非诉讼转诉讼执行成功: {}", response.getMessage());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("非诉讼转诉讼执行失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("非诉讼转诉讼处理过程中发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "非诉讼转诉讼失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}